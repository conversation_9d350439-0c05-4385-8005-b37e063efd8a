#!/usr/bin/env python3  # 指定Python解释器路径
# -*- coding: utf-8 -*-  # 指定文件编码为UTF-8
"""
推箱子游戏核心类
包含游戏逻辑、地图管理、状态控制等功能
"""

import pygame  # 导入pygame游戏开发库
import os  # 导入操作系统相关模块
import sys  # 导入系统相关模块
from typing import List, Tuple, Optional  # 导入类型提示相关模块

def get_resource_path(relative_path):
    """获取资源文件的绝对路径，兼容PyInstaller打包"""
    try:
        # PyInstaller创建临时文件夹，并将路径存储在_MEIPASS中
        base_path = sys._MEIPASS  # 获取PyInstaller打包后的临时文件夹路径
    except Exception:  # 如果获取失败（未打包运行时）
        base_path = os.path.abspath(".")  # 使用当前工作目录
    return os.path.join(base_path, relative_path)  # 拼接路径并返回

# 地图元素定义
EMPTY = 0      # 空地，表示地图上的空白区域
WALL = 1       # 墙壁，表示不可通过的障碍物
BOX = 2        # 箱子，表示可以推动的箱子
TARGET = 3     # 目标点，表示箱子需要放置的位置
PLAYER = 4     # 玩家，表示玩家当前的位置
BOX_ON_TARGET = 5  # 箱子在目标点上，表示箱子已正确放置

# 颜色定义，用于游戏渲染
COLORS = {
    'WHITE': (255, 255, 255),  # 白色，RGB值
    'BLACK': (0, 0, 0),  # 黑色，RGB值
    'BROWN': (139, 69, 19),  # 棕色，RGB值
    'BLUE': (0, 0, 255),  # 蓝色，RGB值
    'RED': (255, 0, 0),  # 红色，RGB值
    'GREEN': (0, 255, 0),  # 绿色，RGB值
    'YELLOW': (255, 255, 0),  # 黄色，RGB值
    'GRAY': (128, 128, 128),  # 灰色，RGB值
    'ORANGE': (255, 165, 0),  # 橙色，RGB值
    'PURPLE': (128, 0, 128)  # 紫色，RGB值
}

class SokobanGame:
    """推箱子游戏主类"""
    
    def __init__(self, screen):
        """初始化游戏"""
        self.screen = screen  # 保存游戏屏幕对象
        self.screen_width = screen.get_width()  # 获取屏幕宽度
        self.screen_height = screen.get_height()  # 获取屏幕高度

        # 游戏状态
        self.current_level = 1  # 当前关卡号，初始为1
        self.steps = 0  # 当前关卡步数，初始为0
        self.game_completed = False  # 游戏是否全部完成，初始为False
        self.level_completed = False  # 当前关卡是否完成，初始为False
        self.allow_skip_level = True  # 是否允许跳过关卡，初始为False（只能完成后进入下一关），可改为True允许随时跳关

        # 玩家方向（用于选择正确的动作帧）
        self.player_direction = 'down'  # 玩家朝向，默认向下，可选值：down, up, left, right

        # 地图相关
        self.map_data = []  # 地图数据，二维数组表示
        self.player_pos = [0, 0]  # 玩家位置，[x, y]坐标
        self.targets = []  # 目标点位置列表
        self.boxes = []  # 箱子位置列表
        self.walls = []  # 墙壁位置列表

        # 渲染相关
        self.tile_size = 40  # 每个方块的像素大小
        self.map_offset_x = 0  # 地图在屏幕上的X轴偏移量
        self.map_offset_y = 0  # 地图在屏幕上的Y轴偏移量

        # 加载游戏素材
        self.load_sprites()  # 调用方法加载游戏精灵图像
        
        # 字体 - 使用系统中文字体
        pygame.font.init()  # 初始化pygame字体模块

        # 常见的Windows中文字体列表
        chinese_fonts = [
            'microsoftyahei',  # 微软雅黑
            'simhei',          # 黑体
            'simsun',          # 宋体
            'kaiti',           # 楷体
            'fangsong',        # 仿宋
            'arial unicode ms' # Arial Unicode MS
        ]

        self.font = None  # 主字体对象，初始为None
        self.small_font = None  # 小字体对象，初始为None

        # 尝试加载中文字体
        for font_name in chinese_fonts:  # 遍历中文字体列表
            try:
                test_font = pygame.font.SysFont(font_name, 28)  # 尝试加载字体，大小为28
                # 测试是否能正确渲染中文
                test_surface = test_font.render('测试', True, (0, 0, 0))  # 渲染测试文本
                if test_surface.get_width() > 0:  # 如果渲染成功（宽度大于0）
                    self.font = test_font  # 保存主字体
                    self.small_font = pygame.font.SysFont(font_name, 20)  # 保存小字体，大小为20
                    print(f"使用字体: {font_name}")  # 输出使用的字体名称
                    break  # 找到可用字体，退出循环
            except:  # 如果加载失败
                continue  # 继续尝试下一个字体

        # 如果没有找到合适的中文字体，使用默认字体
        if self.font is None:  # 如果没有找到中文字体
            self.font = pygame.font.Font(None, 36)  # 使用默认字体，大小为36
            self.small_font = pygame.font.Font(None, 24)  # 使用默认小字体，大小为24
            print("Warning: No Chinese font found, using default font")  # 输出警告信息
            self.use_chinese = False  # 标记不使用中文
        else:
            self.use_chinese = True  # 标记使用中文
        
        # 加载关卡
        self.load_level(self.current_level)  # 加载第一关

    def load_sprites(self):
        """加载游戏素材"""
        try:
            # 加载基础素材
            self.wall_sprite = pygame.image.load(get_resource_path("image/wall.png"))  # 加载墙壁图像
            self.box_sprite = pygame.image.load(get_resource_path("image/box.png"))  # 加载箱子图像
            self.box_finished_sprite = pygame.image.load(get_resource_path("image/boxfinished.png"))  # 加载完成状态的箱子图像
            self.destination_sprite = pygame.image.load(get_resource_path("image/destination.png"))  # 加载目标点图像

            # 加载角色素材表
            character_sheet = pygame.image.load(get_resource_path("image/character.png"))  # 加载角色精灵表

            # 缩放基础素材到合适大小
            self.wall_sprite = pygame.transform.scale(self.wall_sprite, (self.tile_size, self.tile_size))  # 缩放墙壁图像
            self.box_sprite = pygame.transform.scale(self.box_sprite, (self.tile_size, self.tile_size))  # 缩放箱子图像
            self.box_finished_sprite = pygame.transform.scale(self.box_finished_sprite, (self.tile_size, self.tile_size))  # 缩放完成状态的箱子图像
            self.destination_sprite = pygame.transform.scale(self.destination_sprite, (self.tile_size, self.tile_size))  # 缩放目标点图像

            # 提取角色动作帧（假设是4x4的网格，每个方向一行）
            frame_size = 32  # 原始帧大小，32x32像素
            self.character_sprites = {}  # 创建角色精灵字典

            # 方向映射：下、左、右、上（常见的精灵表布局）
            directions = ['down', 'left', 'right', 'up']  # 定义四个方向

            for i, direction in enumerate(directions):  # 遍历每个方向
                # 提取该方向的第一帧（静止状态）
                frame_rect = pygame.Rect(0, i * frame_size, frame_size, frame_size)  # 创建矩形区域，指定要提取的帧位置
                frame = character_sheet.subsurface(frame_rect)  # 从精灵表中提取指定区域
                # 缩放到游戏大小
                self.character_sprites[direction] = pygame.transform.scale(frame, (self.tile_size, self.tile_size))  # 缩放并保存到字典

            self.use_sprites = True  # 标记成功加载精灵
            print("✓ 游戏素材加载成功")  # 输出成功信息

        except Exception as e:  # 如果加载失败
            print(f"✗ 素材加载失败，使用默认颜色: {e}")  # 输出错误信息
            self.use_sprites = False  # 标记不使用精灵，将使用颜色块渲染
    
    def load_level(self, level_num: int) -> bool:
        """加载指定关卡"""
        try:
            level_file = get_resource_path(f"levels/level_{level_num}.txt")  # 获取关卡文件路径

            if not os.path.exists(level_file):  # 检查关卡文件是否存在
                print(f"关卡文件不存在: {level_file}")  # 输出警告信息
                # 如果文件不存在，创建默认关卡
                self.create_default_levels()  # 调用方法创建默认关卡
                if not os.path.exists(level_file):  # 再次检查文件是否存在
                    print(f"无法创建关卡文件: {level_file}")  # 输出错误信息
                    return False  # 返回失败

            with open(level_file, 'r', encoding='utf-8') as f:  # 打开关卡文件
                lines = f.readlines()  # 读取所有行

            # 解析地图数据
            self.map_data = []  # 初始化地图数据
            self.targets = []  # 初始化目标点列表
            self.boxes = []  # 初始化箱子列表
            self.walls = []  # 初始化墙壁列表

            # 首先找到最大行长度
            max_width = 0  # 初始化最大宽度
            for line in lines:  # 遍历所有行
                line_content = line.rstrip('\n\r')  # 去除行尾的换行符
                max_width = max(max_width, len(line_content))  # 更新最大宽度

            # 如果地图为空或太小，使用默认地图
            if max_width == 0 or len(lines) == 0:  # 检查地图是否有效
                print(f"关卡 {level_num} 地图数据无效，使用默认地图")  # 输出警告信息
                lines = [  # 使用默认地图数据
                    "########",
                    "#  .   #",
                    "#  $   #",
                    "#  @   #",
                    "#      #",
                    "########"
                ]
                max_width = 8  # 设置默认宽度

            for y, line in enumerate(lines):  # 遍历所有行，y为行号
                row = []  # 创建新行
                line_content = line.rstrip('\n\r')  # 去除行尾的换行符
                # 确保每行都有相同的长度，不足的用空格补齐
                line_content = line_content.ljust(max_width)  # 用空格填充到最大宽度

                for x, char in enumerate(line_content):  # 遍历行中每个字符，x为列号
                    if char == '#':  # 墙壁
                        row.append(WALL)  # 添加墙壁类型
                        self.walls.append([x, y])  # 添加墙壁位置
                    elif char == '$':  # 箱子
                        row.append(EMPTY)  # 箱子下面是空地
                        self.boxes.append([x, y])  # 添加箱子位置
                    elif char == '.':  # 目标点
                        row.append(TARGET)  # 添加目标点类型
                        self.targets.append([x, y])  # 添加目标点位置
                    elif char == '@':  # 玩家
                        row.append(EMPTY)  # 玩家下面是空地
                        self.player_pos = [x, y]  # 设置玩家位置
                    elif char == '*':  # 箱子在目标点上
                        row.append(TARGET)  # 添加目标点类型
                        self.boxes.append([x, y])  # 添加箱子位置
                        self.targets.append([x, y])  # 添加目标点位置
                    elif char == '+':  # 玩家在目标点上
                        row.append(TARGET)  # 添加目标点类型
                        self.player_pos = [x, y]  # 设置玩家位置
                        self.targets.append([x, y])  # 添加目标点位置
                    else:  # 空地（空格或其他字符）
                        row.append(EMPTY)  # 添加空地类型
                self.map_data.append(row)  # 将行添加到地图数据
            
            # 检查地图尺寸是否超出窗口
            if not self.validate_map_size():  # 验证地图尺寸
                import warnings  # 导入警告模块
                warnings.warn(f"关卡 {level_num} 地图尺寸超出窗口范围！", UserWarning)  # 输出警告
                return False  # 返回失败

            # 计算地图偏移，使地图居中显示
            map_width = len(self.map_data[0]) * self.tile_size  # 计算地图宽度
            map_height = len(self.map_data) * self.tile_size  # 计算地图高度
            self.map_offset_x = (self.screen_width - map_width) // 2  # 计算X轴偏移，使地图水平居中
            self.map_offset_y = (self.screen_height - map_height) // 2 + 30  # 计算Y轴偏移，使地图垂直居中，并向下偏移30像素

            # 重置游戏状态
            self.steps = 0  # 重置步数
            self.level_completed = False  # 重置关卡完成状态

            return True  # 返回成功

        except Exception as e:  # 捕获异常
            print(f"加载关卡失败: {e}")  # 输出错误信息
            return False  # 返回失败

    def validate_map_size(self) -> bool:
        """验证地图尺寸是否适合窗口"""
        if not self.map_data:  # 检查地图数据是否存在
            return False  # 返回失败

        # 计算地图实际尺寸
        map_width = len(self.map_data[0]) * self.tile_size  # 计算地图宽度（像素）
        map_height = len(self.map_data) * self.tile_size  # 计算地图高度（像素）

        # 预留UI空间：顶部30px用于显示信息，底部30px用于帮助文本
        available_width = self.screen_width  # 可用宽度等于屏幕宽度
        available_height = self.screen_height - 60  # 可用高度等于屏幕高度减去UI空间（顶部30px + 底部30px）

        # 检查是否超出窗口
        if map_width > available_width:  # 检查地图宽度是否超出可用宽度
            import warnings  # 导入警告模块
            warnings.warn(f"地图宽度 {map_width}px 超出窗口宽度 {available_width}px", UserWarning)  # 输出宽度警告
            warnings.warn(f"建议最大地图宽度：{available_width // self.tile_size} 个方块", UserWarning)  # 输出建议宽度
            return False  # 返回失败

        if map_height > available_height:  # 检查地图高度是否超出可用高度
            import warnings  # 导入警告模块
            warnings.warn(f"地图高度 {map_height}px 超出窗口高度 {available_height}px", UserWarning)  # 输出高度警告
            warnings.warn(f"建议最大地图高度：{available_height // self.tile_size} 个方块", UserWarning)  # 输出建议高度
            return False  # 返回失败

        return True  # 返回成功

    def create_default_levels(self):
        """创建默认关卡文件"""
        levels_dir = get_resource_path("levels")  # 获取关卡目录路径
        if not os.path.exists(levels_dir):  # 检查关卡目录是否存在
            os.makedirs(levels_dir)  # 创建关卡目录

        # 创建第一关
        level1 = """########  # 墙壁边界
#  .   #  # 目标点
#  $   #  # 箱子
#  @   #  # 玩家
#      #  # 空地
########"""  # 墙壁边界

        level_file = get_resource_path("levels/level_1.txt")  # 获取第一关文件路径
        with open(level_file, 'w', encoding='utf-8') as f:  # 打开文件准备写入
            f.write(level1)  # 写入关卡数据

    def handle_input(self, key):
        """处理键盘输入"""
        # 如果关卡已完成，阻止玩家移动
        if self.level_completed:  # 检查关卡是否完成
            return  # 如果完成，直接返回，不处理输入

        # 方向映射
        directions = {  # 定义按键到方向的映射
            pygame.K_UP: (0, -1),  # 上方向键，向上移动
            pygame.K_DOWN: (0, 1),  # 下方向键，向下移动
            pygame.K_LEFT: (-1, 0),  # 左方向键，向左移动
            pygame.K_RIGHT: (1, 0),  # 右方向键，向右移动
            pygame.K_w: (0, -1),  # W键，向上移动
            pygame.K_s: (0, 1),  # S键，向下移动
            pygame.K_a: (-1, 0),  # A键，向左移动
            pygame.K_d: (1, 0)  # D键，向右移动
        }

        if key in directions:  # 检查按键是否在方向映射中
            dx, dy = directions[key]  # 获取移动方向
            # 更新玩家方向
            if dx == 0 and dy == -1:  # 如果向上移动
                self.player_direction = 'up'  # 设置玩家方向为向上
            elif dx == 0 and dy == 1:  # 如果向下移动
                self.player_direction = 'down'  # 设置玩家方向为向下
            elif dx == -1 and dy == 0:  # 如果向左移动
                self.player_direction = 'left'  # 设置玩家方向为向左
            elif dx == 1 and dy == 0:  # 如果向右移动
                self.player_direction = 'right'  # 设置玩家方向为向右

            self.move_player(dx, dy)  # 调用方法移动玩家

    def move_player(self, dx: int, dy: int):
        """移动玩家"""
        new_x = self.player_pos[0] + dx  # 计算新的X坐标
        new_y = self.player_pos[1] + dy  # 计算新的Y坐标

        # 检查边界 - 更严格的检查
        if (new_y < 0 or new_y >= len(self.map_data) or  # 检查Y坐标是否越界
            new_x < 0 or len(self.map_data) == 0):  # 检查X坐标是否越界
            return  # 如果越界，直接返回

        # 检查当前行是否存在以及是否越界
        if new_y >= len(self.map_data) or new_x >= len(self.map_data[new_y]):  # 检查新位置是否在地图范围内
            return  # 如果越界，直接返回

        # 检查目标位置
        target_tile = self.map_data[new_y][new_x]  # 获取目标位置的地图元素

        # 如果是墙壁，不能移动
        if target_tile == WALL:  # 检查目标位置是否为墙壁
            return  # 如果是墙壁，直接返回

        # 如果目标位置有箱子
        if [new_x, new_y] in self.boxes:  # 检查目标位置是否有箱子
            # 检查箱子后面的位置
            box_new_x = new_x + dx  # 计算箱子的新X坐标
            box_new_y = new_y + dy  # 计算箱子的新Y坐标

            # 检查箱子新位置的边界 - 更严格的检查
            if (box_new_y < 0 or box_new_y >= len(self.map_data) or  # 检查箱子Y坐标是否越界
                box_new_x < 0 or len(self.map_data) == 0):  # 检查箱子X坐标是否越界
                return  # 如果越界，直接返回

            # 检查箱子目标行是否存在以及是否越界
            if box_new_y >= len(self.map_data) or box_new_x >= len(self.map_data[box_new_y]):  # 检查箱子新位置是否在地图范围内
                return  # 如果越界，直接返回

            # 检查箱子新位置是否有障碍物
            if (self.map_data[box_new_y][box_new_x] == WALL or  # 检查箱子新位置是否为墙壁
                [box_new_x, box_new_y] in self.boxes):  # 检查箱子新位置是否有其他箱子
                return  # 如果有障碍物，直接返回

            # 移动箱子
            box_index = self.boxes.index([new_x, new_y])  # 获取要移动的箱子索引
            self.boxes[box_index] = [box_new_x, box_new_y]  # 更新箱子位置

        # 移动玩家
        self.player_pos = [new_x, new_y]  # 更新玩家位置
        self.steps += 1  # 增加步数

        # 检查是否完成关卡
        self.check_level_completion()  # 调用方法检查关卡是否完成

    def validate_game_state(self):
        """验证游戏状态的完整性"""
        try:
            # 检查地图数据是否有效
            if not self.map_data or len(self.map_data) == 0:  # 检查地图数据是否存在或为空
                return False  # 如果无效，返回False

            # 检查所有行是否有相同长度
            expected_width = len(self.map_data[0])  # 获取第一行的长度作为期望宽度
            for row in self.map_data:  # 遍历所有行
                if len(row) != expected_width:  # 检查行长度是否与期望宽度相同
                    return False  # 如果不同，返回False

            # 检查玩家位置是否在有效范围内
            if (self.player_pos[1] < 0 or self.player_pos[1] >= len(self.map_data) or  # 检查玩家Y坐标是否越界
                self.player_pos[0] < 0 or self.player_pos[0] >= len(self.map_data[0])):  # 检查玩家X坐标是否越界
                return False  # 如果越界，返回False

            # 检查所有箱子位置是否在有效范围内
            for box in self.boxes:  # 遍历所有箱子
                if (box[1] < 0 or box[1] >= len(self.map_data) or  # 检查箱子Y坐标是否越界
                    box[0] < 0 or box[0] >= len(self.map_data[0])):  # 检查箱子X坐标是否越界
                    return False  # 如果越界，返回False

            return True  # 所有检查通过，返回True
        except Exception:  # 捕获任何异常
            return False  # 发生异常，返回False

    def check_level_completion(self):
        """检查关卡是否完成"""
        # 检查所有目标点是否都有箱子
        for target in self.targets:  # 遍历所有目标点
            if target not in self.boxes:  # 检查目标点上是否有箱子
                return False  # 如果有目标点没有箱子，返回False

        self.level_completed = True  # 所有目标点都有箱子，标记关卡完成
        return True  # 返回True表示关卡完成

    def is_game_completed(self):
        """检查是否完成所有关卡"""
        return self.game_completed  # 返回游戏完成状态

    def reset_level(self):
        """重置当前关卡"""
        self.load_level(self.current_level)  # 重新加载当前关卡

    def next_level(self):
        """下一关"""
        next_level_num = self.current_level + 1  # 计算下一关编号
        # 检查下一关是否存在
        level_file = get_resource_path(f"levels/level_{next_level_num}.txt")  # 获取下一关文件路径
        if os.path.exists(level_file):  # 检查文件是否存在
            if self.load_level(next_level_num):  # 尝试加载下一关
                self.current_level = next_level_num  # 更新当前关卡编号
                return True  # 返回True表示成功进入下一关

        # 如果没有更多关卡，标记游戏完成
        self.game_completed = True  # 标记游戏完成
        return False  # 返回False表示没有更多关卡

    def previous_level(self):
        """上一关"""
        if self.current_level > 1:  # 检查是否不是第一关
            self.current_level -= 1  # 减少关卡编号
            self.load_level(self.current_level)  # 加载上一关
            return True  # 返回True表示成功进入上一关
        return False  # 返回False表示已经是第一关

    def update(self):
        """更新游戏状态"""
        # 验证游戏状态完整性
        if not self.validate_game_state():  # 验证游戏状态是否有效
            print("游戏状态验证失败，尝试重新加载关卡")  # 输出警告信息
            # 尝试重新加载当前关卡
            try:
                self.load_level(self.current_level)  # 重新加载当前关卡
            except Exception as e:  # 捕获异常
                print(f"重新加载关卡失败: {e}")  # 输出错误信息
                raise  # 重新抛出异常

    def render(self):
        """渲染游戏画面"""
        # 清空屏幕
        self.screen.fill(COLORS['WHITE'])  # 用白色填充屏幕

        # 渲染地图
        self.render_map()  # 调用方法渲染地图

        # 渲染UI信息
        self.render_ui()  # 调用方法渲染UI

        # 如果关卡完成，显示完成信息
        if self.level_completed:  # 检查关卡是否完成
            self.render_completion_message()  # 调用方法渲染完成信息

    def render_map(self):
        """渲染地图"""
        for y, row in enumerate(self.map_data):  # 遍历地图的每一行，y为行号
            for x, tile in enumerate(row):  # 遍历行中的每个元素，x为列号，tile为地图元素类型
                pos_x = self.map_offset_x + x * self.tile_size  # 计算屏幕X坐标
                pos_y = self.map_offset_y + y * self.tile_size  # 计算屏幕Y坐标

                rect = pygame.Rect(pos_x, pos_y, self.tile_size, self.tile_size)  # 创建矩形区域

                if self.use_sprites:  # 如果使用精灵渲染
                    # 使用素材渲染

                    # 绘制地面（目标点或普通地面）
                    if [x, y] in self.targets:  # 如果当前位置是目标点
                        self.screen.blit(self.destination_sprite, (pos_x, pos_y))  # 绘制目标点图像
                    else:
                        # 绘制灰色地面
                        pygame.draw.rect(self.screen, COLORS['GRAY'], rect)  # 绘制灰色矩形

                    # 绘制墙壁
                    if tile == WALL:  # 如果当前位置是墙壁
                        self.screen.blit(self.wall_sprite, (pos_x, pos_y))  # 绘制墙壁图像

                    # 绘制箱子
                    if [x, y] in self.boxes:  # 如果当前位置有箱子
                        if [x, y] in self.targets:  # 如果箱子在目标点上
                            self.screen.blit(self.box_finished_sprite, (pos_x, pos_y))  # 绘制完成状态的箱子
                        else:
                            self.screen.blit(self.box_sprite, (pos_x, pos_y))  # 绘制普通箱子

                    # 绘制玩家
                    if [x, y] == self.player_pos:  # 如果当前位置是玩家
                        self.screen.blit(self.character_sprites[self.player_direction], (pos_x, pos_y))  # 绘制玩家图像

                else:  # 如果不使用精灵渲染
                    # 使用颜色块渲染（备选方案）

                    # 绘制地面
                    if [x, y] in self.targets:  # 如果当前位置是目标点
                        pygame.draw.rect(self.screen, COLORS['YELLOW'], rect)  # 绘制黄色矩形
                    else:
                        pygame.draw.rect(self.screen, COLORS['GRAY'], rect)  # 绘制灰色矩形

                    # 绘制墙壁
                    if tile == WALL:  # 如果当前位置是墙壁
                        pygame.draw.rect(self.screen, COLORS['BROWN'], rect)  # 绘制棕色矩形

                    # 绘制箱子
                    if [x, y] in self.boxes:  # 如果当前位置有箱子
                        if [x, y] in self.targets:  # 如果箱子在目标点上
                            pygame.draw.rect(self.screen, COLORS['GREEN'], rect)  # 绘制绿色矩形
                        else:
                            pygame.draw.rect(self.screen, COLORS['ORANGE'], rect)  # 绘制橙色矩形

                    # 绘制玩家
                    if [x, y] == self.player_pos:  # 如果当前位置是玩家
                        pygame.draw.rect(self.screen, COLORS['BLUE'], rect)  # 绘制蓝色矩形

                    # 绘制边框
                    pygame.draw.rect(self.screen, COLORS['BLACK'], rect, 1)  # 绘制黑色边框

    def render_ui(self):
        """渲染UI信息"""
        try:
            if self.use_chinese:  # 如果使用中文界面
                # 中文界面
                level_text = self.font.render(f"关卡: {self.current_level}", True, COLORS['BLACK'])  # 渲染关卡文本
                steps_text = self.font.render(f"步数: {self.steps}", True, COLORS['BLACK'])  # 渲染步数文本
                completed_boxes = sum(1 for box in self.boxes if box in self.targets)  # 计算已完成的箱子数量
                total_boxes = len(self.targets)  # 获取总箱子数量
                progress_text = self.font.render(f"进度: {completed_boxes}/{total_boxes}", True, COLORS['BLACK'])  # 渲染进度文本
                help_text = self.small_font.render("方向键移动 | R重置 | N下一关 | P上一关 | ESC退出", True, COLORS['BLACK'])  # 渲染帮助文本
            else:
                # 英文界面
                level_text = self.font.render(f"Level: {self.current_level}", True, COLORS['BLACK'])  # 渲染关卡文本
                steps_text = self.font.render(f"Steps: {self.steps}", True, COLORS['BLACK'])  # 渲染步数文本
                completed_boxes = sum(1 for box in self.boxes if box in self.targets)  # 计算已完成的箱子数量
                total_boxes = len(self.targets)  # 获取总箱子数量
                progress_text = self.font.render(f"Progress: {completed_boxes}/{total_boxes}", True, COLORS['BLACK'])  # 渲染进度文本
                help_text = self.small_font.render("Arrow Keys: Move | R: Reset | N: Next | P: Prev | ESC: Exit", True, COLORS['BLACK'])  # 渲染帮助文本

            self.screen.blit(level_text, (10, 10))  # 绘制关卡文本
            self.screen.blit(steps_text, (200, 10))  # 绘制步数文本
            self.screen.blit(progress_text, (400, 10))  # 绘制进度文本
            self.screen.blit(help_text, (10, self.screen_height - 30))  # 绘制帮助文本

        except Exception as e:  # 捕获异常
            # 如果渲染失败，使用默认字体和英文
            print(f"Font rendering error: {e}")  # 输出错误信息
            default_font = pygame.font.Font(None, 36)  # 创建默认字体
            level_text = default_font.render(f"Level: {self.current_level}", True, COLORS['BLACK'])  # 渲染关卡文本
            self.screen.blit(level_text, (10, 10))  # 绘制关卡文本

    def render_completion_message(self):
        """渲染关卡完成信息"""
        try:
            # 创建半透明覆盖层
            overlay = pygame.Surface((self.screen_width, self.screen_height))  # 创建覆盖层
            overlay.set_alpha(128)  # 设置透明度（0-255）
            overlay.fill(COLORS['BLACK'])  # 用黑色填充覆盖层
            self.screen.blit(overlay, (0, 0))  # 绘制覆盖层

            # 完成信息
            if self.use_chinese:  # 如果使用中文界面
                if self.game_completed:  # 如果所有关卡都完成
                    message = "恭喜！所有关卡完成！"  # 设置完成信息
                    hint_message = "按 ESC 键返回主界面"  # 设置提示信息
                else:
                    message = f"关卡 {self.current_level} 完成！"  # 设置完成信息
                    hint_message = "按 N 键进入下一关，按 ESC 键返回主界面"  # 设置提示信息
            else:  # 如果使用英文界面
                if self.game_completed:  # 如果所有关卡都完成
                    message = "Congratulations! All levels completed!"  # 设置完成信息
                    hint_message = "Press ESC to return to main menu"  # 设置提示信息
                else:
                    message = f"Level {self.current_level} Completed!"  # 设置完成信息
                    hint_message = "Press N for next level, ESC for main menu"  # 设置提示信息

            text = self.font.render(message, True, COLORS['WHITE'])  # 渲染完成信息文本
            text_rect = text.get_rect(center=(self.screen_width // 2, self.screen_height // 2))  # 计算文本位置（居中）
            self.screen.blit(text, text_rect)  # 绘制文本

            # 提示信息
            if not self.game_completed and hint_message:  # 如果游戏未完成且有提示信息
                hint_text = self.small_font.render(hint_message, True, COLORS['WHITE'])  # 渲染提示信息文本
                hint_rect = hint_text.get_rect(center=(self.screen_width // 2, self.screen_height // 2 + 40))  # 计算文本位置（居中，向下偏移40像素）
                self.screen.blit(hint_text, hint_rect)  # 绘制文本

        except Exception as e:  # 捕获异常
            print(f"Completion message rendering error: {e}")  # 输出错误信息
            # 使用默认字体作为备选
            default_font = pygame.font.Font(None, 36)  # 创建默认字体
            text = default_font.render("Level Completed!", True, COLORS['WHITE'])  # 渲染完成信息文本
            text_rect = text.get_rect(center=(self.screen_width // 2, self.screen_height // 2))  # 计算文本位置（居中）
            self.screen.blit(text, text_rect)  # 绘制文本
