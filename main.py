#!/usr/bin/env python3  # 指定Python解释器路径
# -*- coding: utf-8 -*-  # 指定文件编码为UTF-8
"""
推箱子游戏主程序
作者：熊秋锦、廖云川、叶马可
""" 

import pygame  # 导入pygame游戏开发库
from sokoban_game import SokobanGame  # 导入推箱子游戏类
from main_menu import MainMenu  # 导入主菜单类

# 游戏配置
WINDOW_WIDTH = 1080  # 窗口宽度，默认1080像素
WINDOW_HEIGHT = 900  # 窗口高度，默认900像素
FPS = 30  # 游戏帧率，每秒30帧
TITLE = "推箱子游戏 - Sokoban"  # 游戏窗口标题

def main():
    """游戏主函数"""

    try:
        # 初始化Pygame
        pygame.init()  # 初始化pygame库

        # 创建游戏窗口
        screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))  # 创建游戏窗口，设置初始大小
        pygame.display.set_caption(TITLE)  # 设置窗口标题

        # 创建时钟对象控制帧率
        clock = pygame.time.Clock()  # 创建时钟对象，用于控制游戏帧率

        # 游戏状态管理
        game_state = "menu"  # 当前游戏状态，可选值：menu（主菜单）、playing（游戏中）、settings（设置）
        main_menu = MainMenu(screen)  # 创建主菜单对象
        game = None  # 游戏对象，初始为None

        # 游戏主循环
        running = True  # 游戏运行标志，True表示游戏正在运行

        while running:  # 游戏主循环，持续运行直到running为False
            # 处理事件
            for event in pygame.event.get():  # 遍历所有事件
                if event.type == pygame.QUIT:  # 如果是退出事件
                    running = False  # 设置运行标志为False，退出游戏
                elif game_state == "menu":  # 如果当前在主菜单
                    # 主界面事件处理
                    menu_action = main_menu.handle_input(event)  # 处理菜单输入事件
                    if menu_action == "start_game":  # 如果选择开始游戏
                        # 开始游戏
                        try:
                            game = SokobanGame(screen)  # 创建游戏对象
                            game_state = "playing"  # 切换到游戏状态
                        except Exception as e:  # 捕获异常
                            print(f"游戏初始化失败: {e}")  # 输出错误信息
                            running = False  # 退出游戏
                    elif menu_action == "change_window_size":  # 如果选择更改窗口大小
                        # 改变窗口大小
                        if not main_menu.is_fullscreen:  # 如果不是全屏模式
                            new_size = main_menu.get_current_window_size()  # 获取新的窗口大小
                            screen = pygame.display.set_mode(new_size)  # 设置新的窗口大小
                            main_menu.screen = screen  # 更新菜单的屏幕引用
                            main_menu.screen_width = screen.get_width()  # 更新菜单的屏幕宽度
                            main_menu.screen_height = screen.get_height()  # 更新菜单的屏幕高度
                            # 如果游戏正在运行，也需要更新游戏的屏幕引用
                            if game:  # 如果游戏对象存在
                                game.screen = screen  # 更新游戏的屏幕引用
                                game.screen_width = screen.get_width()  # 更新游戏的屏幕宽度
                                game.screen_height = screen.get_height()  # 更新游戏的屏幕高度
                    elif menu_action == "toggle_fullscreen":  # 如果选择切换全屏
                        # 切换全屏
                        if main_menu.is_fullscreen:  # 如果是全屏模式
                            # 切换到全屏模式
                            screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)  # 设置为全屏模式
                        else:  # 如果是窗口模式
                            # 切换到窗口模式
                            size = main_menu.get_current_window_size()  # 获取窗口大小
                            screen = pygame.display.set_mode(size)  # 设置为窗口模式
                        main_menu.screen = screen  # 更新菜单的屏幕引用
                        main_menu.screen_width = screen.get_width()  # 更新菜单的屏幕宽度
                        main_menu.screen_height = screen.get_height()  # 更新菜单的屏幕高度
                        # 如果游戏正在运行，也需要更新游戏的屏幕引用
                        if game:  # 如果游戏对象存在
                            game.screen = screen  # 更新游戏的屏幕引用
                            game.screen_width = screen.get_width()  # 更新游戏的屏幕宽度
                            game.screen_height = screen.get_height()  # 更新游戏的屏幕高度
                    elif menu_action == "exit":  # 如果选择退出
                        running = False  # 设置运行标志为False，退出游戏
                elif game_state == "playing":  # 如果当前在游戏中
                    # 游戏中事件处理
                    if event.type == pygame.KEYDOWN:  # 如果是按键按下事件
                        if event.key == pygame.K_ESCAPE:  # 如果按下ESC键
                            # ESC键返回主界面
                            game_state = "menu"  # 切换到主菜单状态
                            game = None  # 清除游戏对象
                        elif event.key == pygame.K_r:  # 如果按下R键
                            # R键重置当前关卡
                            try:
                                game.reset_level()  # 重置当前关卡
                            except Exception as e:  # 捕获异常
                                print(f"重置关卡失败: {e}")  # 输出错误信息
                                game_state = "menu"  # 切换到主菜单状态
                                game = None  # 清除游戏对象
                        elif event.key == pygame.K_n:  # 如果按下N键
                            # N键下一关
                            try:
                                # 如果游戏已完成，不允许按N键
                                if game.game_completed:
                                    print("所有关卡已完成，请按ESC键返回主界面")  # 输出提示信息
                                # 根据allow_skip_level标志决定是否可以随时进入下一关
                                elif game.allow_skip_level or game.level_completed:  # 允许跳过或关卡完成时可以进入下一关
                                    if game.next_level():  # 尝试进入下一关
                                        print(f"进入关卡 {game.current_level}")  # 输出关卡信息
                                    else:  # 如果没有更多关卡
                                        print("恭喜！已完成所有关卡")  # 输出完成信息
                                        # 不返回主菜单，让游戏显示完成界面
                                else:
                                    print("当前不允许跳过关卡，请完成当前关卡后再试")  # 输出提示信息
                            except Exception as e:  # 捕获异常
                                print(f"切换到下一关失败: {e}")  # 输出错误信息
                                game_state = "menu"  # 切换到主菜单状态
                                game = None  # 清除游戏对象
                        elif event.key == pygame.K_p:  # 如果按下P键
                            # P键上一关
                            try:
                                if game.previous_level():  # 尝试返回上一关
                                    print(f"返回关卡 {game.current_level}")  # 输出关卡信息
                                else:  # 如果已经是第一关
                                    print("已经是第一关")  # 输出提示信息
                            except Exception as e:  # 捕获异常
                                print(f"切换到上一关失败: {e}")  # 输出错误信息
                                game_state = "menu"  # 切换到主菜单状态
                                game = None  # 清除游戏对象
                        elif event.key in [pygame.K_UP, pygame.K_DOWN, pygame.K_LEFT, pygame.K_RIGHT,
                                         pygame.K_w, pygame.K_s, pygame.K_a, pygame.K_d]:  # 如果按下方向键或WASD键
                            # 只有方向键才传递给handle_input
                            try:
                                game.handle_input(event.key)  # 处理玩家输入
                            except Exception as e:  # 捕获异常
                                print(f"处理玩家输入失败: {e}")  # 输出错误信息
                                game_state = "menu"  # 切换到主菜单状态
                                game = None  # 清除游戏对象

            # 根据游戏状态进行更新和渲染
            if game_state == "menu":  # 如果当前在主菜单
                # 渲染主界面
                main_menu.render()  # 渲染主菜单
            elif game_state == "playing" and game:  # 如果当前在游戏中且游戏对象存在
                # 更新游戏状态
                try:
                    game.update()  # 更新游戏状态
                    # 检查是否完成所有关卡
                    if game.game_completed:  # 如果完成所有关卡
                        print("恭喜！完成所有关卡，返回主界面")  # 输出完成信息
                        game_state = "menu"  # 切换到主菜单状态
                        game = None  # 清除游戏对象
                except Exception as e:  # 捕获异常
                    print(f"游戏状态更新失败: {e}")  # 输出错误信息
                    game_state = "menu"  # 切换到主菜单状态
                    game = None  # 清除游戏对象

                # 渲染游戏画面
                if game:  # 如果游戏对象存在
                    try:
                        game.render()  # 渲染游戏画面
                    except Exception as e:  # 捕获异常
                        print(f"游戏渲染失败: {e}")  # 输出错误信息
                        game_state = "menu"  # 切换到主菜单状态
                        game = None  # 清除游戏对象

            # 更新显示
            pygame.display.flip()  # 更新屏幕显示

            # 控制帧率
            clock.tick(FPS)  # 控制游戏帧率为FPS值

    except Exception as e:  # 捕获所有未处理的异常
        print(f"游戏发生致命错误: {e}")  # 输出错误信息

    finally:  # 无论是否发生异常都会执行
        # 清理资源
        pygame.quit()  # 退出pygame，释放资源

if __name__ == "__main__":  # 如果作为主程序运行
    main()  # 调用主函数
