#!/usr/bin/env python3  # 指定Python解释器路径
# -*- coding: utf-8 -*-  # 指定文件编码为UTF-8
"""
推箱子游戏主界面菜单
包含开始游戏、设置、退出等功能
"""

import pygame  # 导入pygame游戏开发库

# 颜色定义
COLORS = {
    'WHITE': (255, 255, 255),  # 白色，RGB值
    'BLACK': (0, 0, 0),  # 黑色，RGB值
    'GRAY': (128, 128, 128),  # 灰色，RGB值
    'LIGHT_GRAY': (200, 200, 200),  # 浅灰色，RGB值
    'DARK_GRAY': (64, 64, 64),  # 深灰色，RGB值
    'BLUE': (0, 100, 200),  # 蓝色，RGB值
    'LIGHT_BLUE': (100, 150, 255),  # 浅蓝色，RGB值
    'GREEN': (0, 150, 0),  # 绿色，RGB值
    'RED': (200, 0, 0)  # 红色，RGB值
}

class MainMenu:
    """游戏主界面菜单类"""
    
    def __init__(self, screen):
        """初始化主界面"""
        self.screen = screen  # 保存游戏屏幕对象
        self.screen_width = screen.get_width()  # 获取屏幕宽度
        self.screen_height = screen.get_height()  # 获取屏幕高度
        
        # 菜单状态
        self.current_menu = "main"  # 当前菜单，可选值：main（主菜单），settings（设置菜单）
        self.selected_option = 0  # 当前选中的菜单选项索引
        self.menu_options = []  # 菜单选项列表
        
        # 字体初始化
        self.init_fonts()  # 调用方法初始化字体
        
        # 设置选项
        self.window_sizes = [  # 可用的窗口大小列表
            (800, 600),  # 800x600分辨率
            (1024, 768),  # 1024x768分辨率
            (1280, 720),  # 1280x720分辨率
            (1366, 768),  # 1366x768分辨率
            (1920, 1080)  # 1920x1080分辨率
        ]
        self.current_window_size_index = 2  # 当前窗口大小索引，默认为2（1280x720）
        self.is_fullscreen = False  # 是否全屏，默认为False
        
        # 初始化菜单选项
        self.init_menu_options()  # 调用方法初始化菜单选项
        
    def init_fonts(self):
        """初始化字体"""
        pygame.font.init()  # 初始化pygame字体模块
        
        # 常见的Windows中文字体列表
        chinese_fonts = [
            'microsoftyahei',  # 微软雅黑
            'simhei',          # 黑体
            'simsun',          # 宋体
            'kaiti',           # 楷体
            'fangsong',        # 仿宋
            'arial unicode ms' # Arial Unicode MS
        ]
        
        self.title_font = None  # 标题字体，初始为None
        self.menu_font = None  # 菜单字体，初始为None
        self.small_font = None  # 小字体，初始为None
        
        # 尝试加载中文字体
        for font_name in chinese_fonts:  # 遍历中文字体列表
            try:
                test_font = pygame.font.SysFont(font_name, 48)  # 尝试加载字体，大小为48
                # 测试是否能正确渲染中文
                test_surface = test_font.render('测试', True, (0, 0, 0))  # 渲染测试文本
                if test_surface.get_width() > 0:  # 如果渲染成功（宽度大于0）
                    self.title_font = test_font  # 保存标题字体
                    self.menu_font = pygame.font.SysFont(font_name, 32)  # 保存菜单字体，大小为32
                    self.small_font = pygame.font.SysFont(font_name, 24)  # 保存小字体，大小为24
                    self.use_chinese = True  # 标记使用中文
                    break  # 找到可用字体，退出循环
            except:  # 如果加载失败
                continue  # 继续尝试下一个字体
        
        # 如果没有找到合适的中文字体，使用默认字体
        if self.title_font is None:  # 如果没有找到中文字体
            self.title_font = pygame.font.Font(None, 64)  # 使用默认字体作为标题字体，大小为64
            self.menu_font = pygame.font.Font(None, 48)  # 使用默认字体作为菜单字体，大小为48
            self.small_font = pygame.font.Font(None, 32)  # 使用默认字体作为小字体，大小为32
            self.use_chinese = False  # 标记不使用中文
            
    def init_menu_options(self):
        """初始化菜单选项"""
        if self.current_menu == "main":  # 如果当前是主菜单
            if self.use_chinese:  # 如果使用中文
                self.menu_options = [  # 设置中文菜单选项
                    "开始游戏",  # 开始游戏选项
                    "游戏设置",  # 游戏设置选项
                    "退出游戏"  # 退出游戏选项
                ]
            else:  # 如果使用英文
                self.menu_options = [  # 设置英文菜单选项
                    "Start Game",  # 开始游戏选项
                    "Settings",  # 游戏设置选项
                    "Exit Game"  # 退出游戏选项
                ]
        elif self.current_menu == "settings":  # 如果当前是设置菜单
            if self.use_chinese:  # 如果使用中文
                if self.is_fullscreen:  # 如果全屏模式开启
                    window_size_text = "窗口大小: (全屏模式下不可用)"  # 显示不可用提示
                else:  # 如果全屏模式关闭
                    window_size_text = f"窗口大小: {self.window_sizes[self.current_window_size_index][0]}x{self.window_sizes[self.current_window_size_index][1]}"  # 显示当前窗口大小
                fullscreen_text = f"全屏模式: {'开启' if self.is_fullscreen else '关闭'}"  # 显示全屏模式状态
                self.menu_options = [  # 设置中文菜单选项
                    window_size_text,  # 窗口大小选项
                    fullscreen_text,  # 全屏模式选项
                    "返回主菜单"  # 返回主菜单选项
                ]
            else:  # 如果使用英文
                if self.is_fullscreen:  # 如果全屏模式开启
                    window_size_text = "Window Size: (Unavailable in fullscreen)"  # 显示不可用提示
                else:  # 如果全屏模式关闭
                    window_size_text = f"Window Size: {self.window_sizes[self.current_window_size_index][0]}x{self.window_sizes[self.current_window_size_index][1]}"  # 显示当前窗口大小
                fullscreen_text = f"Fullscreen: {'On' if self.is_fullscreen else 'Off'}"  # 显示全屏模式状态
                self.menu_options = [  # 设置英文菜单选项
                    window_size_text,  # 窗口大小选项
                    fullscreen_text,  # 全屏模式选项
                    "Back to Main"  # 返回主菜单选项
                ]
                
        # 确保选中的选项在有效范围内
        if self.selected_option >= len(self.menu_options):  # 检查选中的选项是否超出范围
            self.selected_option = 0  # 重置选中选项为第一个
            
    def handle_input(self, event):
        """处理输入事件"""
        if event.type == pygame.KEYDOWN:  # 如果是按键按下事件
            if event.key == pygame.K_UP:  # 如果按下上方向键
                self.selected_option = (self.selected_option - 1) % len(self.menu_options)  # 选择上一个选项（循环）
            elif event.key == pygame.K_DOWN:  # 如果按下下方向键
                self.selected_option = (self.selected_option + 1) % len(self.menu_options)  # 选择下一个选项（循环）
            elif event.key == pygame.K_RETURN or event.key == pygame.K_SPACE:  # 如果按下回车键或空格键
                return self.select_option()  # 调用方法处理选项选择
            elif event.key == pygame.K_ESCAPE:  # 如果按下ESC键
                if self.current_menu == "settings":  # 如果当前在设置菜单
                    self.current_menu = "main"  # 切换到主菜单
                    self.selected_option = 0  # 重置选中选项
                    self.init_menu_options()  # 初始化菜单选项
                else:  # 如果在主菜单
                    return "exit"  # 返回退出命令
        return None  # 返回None表示没有特殊操作

    def get_current_window_size(self):
        """获取当前窗口大小"""
        return self.window_sizes[self.current_window_size_index]  # 返回当前窗口大小的元组

    def render(self):
        """渲染主界面"""
        # 清空屏幕
        self.screen.fill(COLORS['WHITE'])  # 用白色填充屏幕

        # 绘制背景渐变效果
        self.draw_background()  # 调用方法绘制背景

        # 绘制标题
        self.draw_title()  # 调用方法绘制标题

        # 绘制菜单选项
        self.draw_menu_options()  # 调用方法绘制菜单选项

        # 绘制底部信息
        self.draw_footer()  # 调用方法绘制底部信息

    def draw_background(self):
        """绘制背景"""
        # 简单的渐变背景效果
        for y in range(self.screen_height):  # 遍历屏幕的每一行
            color_ratio = y / self.screen_height  # 计算颜色比例
            r = int(240 + (255 - 240) * color_ratio)  # 计算红色分量
            g = int(248 + (255 - 248) * color_ratio)  # 计算绿色分量
            b = int(255)  # 蓝色分量保持不变
            color = (r, g, b)  # 创建颜色元组
            pygame.draw.line(self.screen, color, (0, y), (self.screen_width, y))  # 绘制渐变线条

    def draw_title(self):
        """绘制标题"""
        if self.use_chinese:  # 如果使用中文
            title_text = "推箱子游戏"  # 设置中文标题
            subtitle_text = "Sokoban Game"  # 设置英文副标题
        else:  # 如果使用英文
            title_text = "Sokoban Game"  # 设置英文标题
            subtitle_text = "Push the boxes to targets"  # 设置英文副标题

        # 主标题
        title_surface = self.title_font.render(title_text, True, COLORS['DARK_GRAY'])  # 渲染主标题文本
        title_rect = title_surface.get_rect(center=(self.screen_width // 2, self.screen_height // 4))  # 计算标题位置（水平居中，垂直在1/4处）
        self.screen.blit(title_surface, title_rect)  # 绘制标题

        # 副标题
        subtitle_surface = self.small_font.render(subtitle_text, True, COLORS['GRAY'])  # 渲染副标题文本
        subtitle_rect = subtitle_surface.get_rect(center=(self.screen_width // 2, self.screen_height // 4 + 60))  # 计算副标题位置（水平居中，垂直在1/4处向下偏移60像素）
        self.screen.blit(subtitle_surface, subtitle_rect)  # 绘制副标题

    def draw_menu_options(self):
        """绘制菜单选项"""
        start_y = self.screen_height // 2  # 菜单起始Y坐标（屏幕垂直中心）
        option_height = 60  # 选项间距

        for i, option in enumerate(self.menu_options):  # 遍历所有菜单选项
            y_pos = start_y + i * option_height  # 计算当前选项的Y坐标

            # 检查选项是否可用（在设置菜单中，全屏模式下窗口大小不可用）
            is_disabled = (self.current_menu == "settings" and i == 0 and self.is_fullscreen)  # 检查是否禁用

            # 绘制选项背景
            if i == self.selected_option and not is_disabled:  # 如果是选中且未禁用的选项
                # 选中状态
                bg_color = COLORS['LIGHT_BLUE']  # 背景色为浅蓝色
                text_color = COLORS['WHITE']  # 文字颜色为白色
                # 绘制选中背景
                option_rect = pygame.Rect(self.screen_width // 2 - 150, y_pos - 20, 300, 40)  # 创建选项矩形区域
                pygame.draw.rect(self.screen, bg_color, option_rect, border_radius=10)  # 绘制圆角矩形背景
                pygame.draw.rect(self.screen, COLORS['BLUE'], option_rect, 2, border_radius=10)  # 绘制圆角矩形边框
            else:  # 如果是未选中或禁用的选项
                # 未选中状态或禁用状态
                if is_disabled:  # 如果禁用
                    text_color = COLORS['LIGHT_GRAY']  # 禁用选项显示为浅灰色
                else:  # 如果未选中
                    text_color = COLORS['DARK_GRAY']  # 未选中选项显示为深灰色

            # 绘制选项文本
            option_surface = self.menu_font.render(option, True, text_color)  # 渲染选项文本
            option_rect = option_surface.get_rect(center=(self.screen_width // 2, y_pos))  # 计算文本位置（水平居中）
            self.screen.blit(option_surface, option_rect)  # 绘制选项文本

    def draw_footer(self):
        """绘制底部信息"""
        if self.use_chinese:  # 如果使用中文
            if self.current_menu == "main":  # 如果在主菜单
                footer_text = "使用方向键选择，回车确认，ESC退出"  # 设置中文帮助文本
            else:  # 如果在设置菜单
                footer_text = "使用方向键选择，回车确认，ESC返回"  # 设置中文帮助文本
        else:  # 如果使用英文
            if self.current_menu == "main":  # 如果在主菜单
                footer_text = "Use arrow keys to select, Enter to confirm, ESC to exit"  # 设置英文帮助文本
            else:  # 如果在设置菜单
                footer_text = "Use arrow keys to select, Enter to confirm, ESC to go back"  # 设置英文帮助文本

        footer_surface = self.small_font.render(footer_text, True, COLORS['GRAY'])  # 渲染底部文本
        footer_rect = footer_surface.get_rect(center=(self.screen_width // 2, self.screen_height - 50))  # 计算文本位置（水平居中，垂直距离底部50像素）
        self.screen.blit(footer_surface, footer_rect)  # 绘制底部文本
        
    def select_option(self):
        """选择菜单选项"""
        if self.current_menu == "main":  # 如果当前在主菜单
            if self.selected_option == 0:  # 如果选中第一个选项（开始游戏）
                return "start_game"  # 返回开始游戏命令
            elif self.selected_option == 1:  # 如果选中第二个选项（设置）
                self.current_menu = "settings"  # 切换到设置菜单
                self.selected_option = 0  # 重置选中选项
                self.init_menu_options()  # 初始化菜单选项
            elif self.selected_option == 2:  # 如果选中第三个选项（退出）
                return "exit"  # 返回退出命令
        elif self.current_menu == "settings":  # 如果当前在设置菜单
            if self.selected_option == 0:  # 如果选中第一个选项（窗口大小）
                # 只有在非全屏模式下才能更改窗口大小
                if not self.is_fullscreen:  # 如果不是全屏模式
                    self.current_window_size_index = (self.current_window_size_index + 1) % len(self.window_sizes)  # 切换到下一个窗口大小（循环）
                    self.init_menu_options()  # 初始化菜单选项
                    return "change_window_size"  # 返回更改窗口大小命令
            elif self.selected_option == 1:  # 如果选中第二个选项（全屏）
                # 先切换全屏状态，然后返回动作
                self.is_fullscreen = not self.is_fullscreen  # 切换全屏状态
                self.init_menu_options()  # 初始化菜单选项
                return "toggle_fullscreen"  # 返回切换全屏命令
            elif self.selected_option == 2:  # 如果选中第三个选项（返回）
                self.current_menu = "main"  # 切换到主菜单
                self.selected_option = 0  # 重置选中选项
                self.init_menu_options()  # 初始化菜单选项
        return None  # 返回None表示没有特殊操作
